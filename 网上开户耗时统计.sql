网上开户业务耗时统计脚本：set RQ=${RQ};

--包含指标
--WSKH_YWSHSC 网上开户业务审核时长（总时长，单位：分钟）
--WSKH_YWPJMBSHSC 网上开户业务平均每笔审核时长（单位：分钟）
--WSKH_YWPJMBDHSC 网上开户业务平均每笔等候审核时长（单位：分钟）
--WSKH_YWFHSC 网上开户业务复核时长（总时长，单位：分钟）
--WSKH_YWPJMBFHSC 网上开户业务平均每笔复核时长（单位：分钟）
--WSKH_YWPJMBBLSC 网上开户业务平均每笔办理时长（单位：分钟）
--WSKH_YGYWSHSC 各审核人员网上开户业务审核时长（总时长，单位：分钟）
--WSKH_YGYWPJMBSHSC 各审核人员网上开户业务平均每笔审核时长（单位：分钟）
--WSKH_YGYWPJMBDHSC 各审核人员网上开户业务平均每笔等候审核时长（单位：分钟）
--WSKH_YGYWFHSC 各复核人员网上开户业务复核时长（总时长，单位：分钟）
--WSKH_YGYWPJMBFHSC 各复核人员网上开户业务平均每笔复核时长（单位：分钟）
--WSKH_YGYWPJMBBLSC 各处理人员网上开户业务平均每笔办理时长（单位：分钟）

-- CZLX代码说明（基于实际数据分析）：
-- 180: 业务提交 - 业务开始标志
-- 201: 认领操作 - 业务处理开始标志
-- 301: 审核通过 - 各审核环节通过
-- 311: 发回整改 - 审核不通过，退回整改
-- 321: 审批节点操作 - 包括认领和各种审批节点处理
-- 注：180和201都存在，分别表示业务提交和认领操作

-- ========== 时长计算逻辑说明 ==========
-- 1. 等候时长(dhsc): 从认领(201)到审批节点(321)开始的等待时间
--    计算逻辑: 配对相邻的201->321操作，计算时间差
--    适用场景: 业务被认领后等待进入审批流程的时间
--
-- 2. 审核时长(shsc): 从审批节点(321)开始到审核结束(301/311)的处理时间
--    计算逻辑: 配对相邻的321->301/311操作，计算时间差
--    适用场景: 审核人员实际审核处理的时间
--
-- 3. 复核时长(fhsc): 复核环节从审批节点(321)到复核结束(301/311)的处理时间
--    计算逻辑: 配对相邻的321->301/311操作，限定复核节点(jdmc包含'非现复审')
--    适用场景: 复核人员实际复核处理的时间
--
-- 4. 办理时长(blsc): 等候时长+审核时长+复核时长的总和
--    计算逻辑: 将同一业务的各环节时长累加
--    适用场景: 业务从开始到结束的总处理时间
--
-- 5. 时间计算方式: 使用UNIX_TIMESTAMP函数计算秒数差值，除以60转换为分钟
-- ========================================

-- ========== 业务识别逻辑 ==========
-- 识别网上开户业务: 通过step状态和开户方式(khfs)筛选
-- sqrq = ${RQ}: 指定日期的申请
-- step not in (-9,0): 排除无效状态
-- khfs in (3,5): 3=双向视频开户, 5=单向视频开户
create temporary view wskh_shl as
select lc.ywqqid
from LCFXCKH_mapping lc
where lc.sqrq = ${RQ} and lc.step not in (-9,0) and lc.khfs in (3,5);

-- ========== 等候时长计算数据准备 ==========
-- 目标: 计算从认领(201)到审批节点(321)的等候时间
-- 逻辑: 只关注201->321的操作配对，简化筛选条件
-- 筛选条件:
--   1. CZLX IN (201,321): 只包含认领和审批节点操作
--   2. 移除JDMC筛选: 201认领、321审批节点等操作不一定在特定审核环节
--   3. 按时间排序: 使用CONCAT(CZRQ,' ',CZSJ)确保时间顺序
CREATE TEMPORARY VIEW t_dwd_dh AS
 SELECT *,
    ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY CONCAT(CZRQ,' ',CZSJ)) as row_num
    FROM TYWQQ_CZLS_mapping a
    WHERE CZLX IN (201, 321)
    and ywqqid in (select ywqqid from wskh_shl);

-- ========== 等候时长计算 ==========
-- 计算逻辑: 配对201认领操作和321审批节点操作，计算等候时间差
-- 配对方式: 直接配对201->321的相邻操作，确保准确性
-- 时间计算: UNIX_TIMESTAMP转换为秒数，后续除以60转为分钟
-- 分组统计: 按业务ID和操作人员分组求和
CREATE TEMPORARY VIEW dhsc_temp AS
select  ywqqid,czr,sum(sjc) AS dhsc
from (
    SELECT
        t1.ywqqid,
        t2.czr,
        (UNIX_TIMESTAMP(CONCAT(t2.CZRQ,' ',t2.CZSJ), 'yyyyMMdd HH:mm:ss') -
         UNIX_TIMESTAMP(CONCAT(t1.CZRQ,' ',t1.CZSJ), 'yyyyMMdd HH:mm:ss')) AS sjc
    FROM
        (SELECT * FROM t_dwd_dh WHERE CZLX = 201) t1  -- 认领操作
    JOIN
        (SELECT * FROM t_dwd_dh WHERE CZLX = 321) t2  -- 审批节点操作
    ON
        t1.ywqqid = t2.ywqqid AND t1.row_num + 1 = t2.row_num  -- 相邻操作配对
) a
group by ywqqid,czr;

-- 等候时长结果处理: 转换为分钟并计算平均值
create temporary view dhsc as
select czr,nvl(pjsc,0) pjsc,'WSKH_YGYWPJMBDHSC' as idx_code from (
    select round(avg(dhsc)/60,2) as pjsc,czr from dhsc_temp d group by czr) b;

-- ========== 审核时长计算数据准备 ==========
-- 目标: 计算审核环节的处理时间
-- 逻辑: 识别321(审批节点)到301/311(审核结束)的时间差
-- 筛选条件:
--   1. CZLX IN (301,311,321): 审核相关操作
--   2. 移除JDMC筛选: 321审批节点、301通过、311退回等操作不一定在特定审核环节
--   3. 按时间排序: 使用CONCAT(CZRQ,' ',CZSJ)确保时间顺序
create temporary view t_dwd_sh as
 SELECT *,
    ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY CONCAT(CZRQ,' ',CZSJ)) as row_num
    FROM TYWQQ_CZLS_mapping a
    WHERE CZLX IN (301, 311, 321)
    and ywqqid in (select ywqqid from wskh_shl);

-- ========== 审核时长计算 ==========
-- 计算逻辑: 配对321(审批节点开始)和301/311(审核结束)操作
-- 配对条件: 同一业务ID下，相邻的row_num(start.row_num + 1 = end.row_num)
-- 时间计算: 结束时间 - 开始时间，单位为秒
-- 分组统计: 按业务ID和操作人分组求和，处理一个业务可能有多个审核环节
create temporary view shsc_temp as
select  ywqqid,czr,sum(sjc) AS shsc
from (
    SELECT
        start.YWQQID,
        start.ID AS start_id,
        start.CZSJ AS start_time,
        end.CZSJ AS end_time,
        end.czr AS czr,
        UNIX_TIMESTAMP(CONCAT(end.CZRQ,' ',end.CZSJ), 'yyyyMMdd HH:mm:ss') - UNIX_TIMESTAMP(CONCAT(start.CZRQ,' ',start.CZSJ), 'yyyyMMdd HH:mm:ss') AS sjc
    FROM
        (SELECT * FROM t_dwd_sh WHERE CZLX = 321) start  -- 审批节点开始
    JOIN
        (SELECT * FROM t_dwd_sh WHERE CZLX IN (301, 311)) end  -- 审核结束(通过或退回)
    ON
        start.YWQQID = end.YWQQID AND start.row_num + 1 = end.row_num  -- 相邻操作配对
    ORDER BY
        start.YWQQID, start_time
)
group by ywqqid,czr;

-- 审核时长结果处理: 转换为分钟
-- 平均每笔审核时长: 按人员计算平均值
create temporary view shsc as
select czr,nvl(pjsc,0) pjsc,'WSKH_YGYWPJMBSHSC' as idx_code from (
    select round(avg(shsc)/60,2) as pjsc,czr from shsc_temp d group by czr) b;

-- 各审核人员总审核时长: 按人员计算总和
create temporary view zshsc as
select czr,nvl(pjsc,0) pjsc,'WSKH_YGYWSHSC' as idx_code from (
    select round(sum(shsc)/60,2) as pjsc,czr from shsc_temp d group by czr) b;

-- ========== 复核时长计算数据准备 ==========
-- 目标: 计算复核环节的处理时间
-- 逻辑: 识别321(审批节点)到301/311(复核结束)的时间差
-- 筛选条件:
--   1. CZLX IN (301,311,321): 复核相关操作
--   2. jdmc包含'非现复审': 限定复核环节
--   3. 按时间排序: 使用CONCAT(CZRQ,' ',CZSJ)确保时间顺序
create temporary view t_dwd_fh as
 SELECT *,
    ROW_NUMBER() OVER(PARTITION BY YWQQID ORDER BY CONCAT(CZRQ,' ',CZSJ)) as row_num
    FROM TYWQQ_CZLS_mapping a
    WHERE CZLX IN (301, 311, 321)
    and ywqqid in (select ywqqid from wskh_shl)
    and instr(jdmc,'非现复审') > 0;

create temporary view fhsc_temp as
select  ywqqid,czr,sum(sjc) AS fhsc 
from (
    SELECT 
        start.YWQQID, 
        start.ID AS start_id, 
        start.CZSJ AS start_time, 
        end.CZSJ AS end_time, 
        end.czr AS czr,
        UNIX_TIMESTAMP(CONCAT(end.CZRQ,' ',end.CZSJ), 'yyyyMMdd HH:mm:ss') - UNIX_TIMESTAMP(CONCAT(start.CZRQ,' ',start.CZSJ), 'yyyyMMdd HH:mm:ss') AS sjc
    FROM 
        (SELECT * FROM t_dwd_fh WHERE CZLX = 321) start
    JOIN
        (SELECT * FROM t_dwd_fh WHERE CZLX IN (301, 311)) end
    ON 
        start.YWQQID = end.YWQQID AND start.row_num + 1 = end.row_num
    ORDER BY 
        start.YWQQID, start_time
)
group by ywqqid,czr;

--平均每笔复核时长
create temporary view fhsc as
select czr,nvl(pjsc,0) pjsc,'WSKH_YGYWPJMBFHSC' as idx_code from (
    select round(avg(fhsc)/60,2) as pjsc,czr from fhsc_temp d group by czr) b;

--各复核人员网上开户业务复核时长
create temporary view zfhsc as
select czr,nvl(pjsc,0) pjsc,'WSKH_YGYWFHSC' as idx_code from (
    select round(sum(fhsc)/60,2) as pjsc,czr from fhsc_temp d group by czr) b;

-- ========== 办理时长计算 ==========
-- 目标: 计算总的业务办理时长
-- 逻辑: 合并等候时长和审核时长（审核时长已包含复核时长）
-- 注意: 复核时长是审核时长的子集，不应重复计算
create temporary view blsc_temp as
select ywqqid,czr,sum(sc) as sc
from (
    select ywqqid,czr,shsc as sc from shsc_temp  -- 审核时长（包含复核）
    union all
    select ywqqid,czr,dhsc as sc from dhsc_temp  -- 等候时长
) a
group by ywqqid,czr;

create temporary view blsc as
select czr,nvl(pjsc,0) pjsc,'WSKH_YGYWPJMBBLSC' as idx_code from (
    select round(avg(sc)/60,2) as pjsc,czr from blsc_temp d group by czr) b;

-- ========== 总体指标计算 ==========
-- 网上开户业务审核时长（总时长）: 所有人员审核时长的总和
create temporary view rjshsc as
select COALESCE(round(sum(w.zshsc)/60,2), 0) as pjsc from (
select sum(shsc) as zshsc,czr from shsc_temp d group by czr) w;

-- 网上开户业务平均每笔审核时长: 直接对所有业务记录求平均
create temporary view pjshsc as
select COALESCE(round(avg(shsc)/60,2), 0) as pjsc from shsc_temp;

--网上开户业务平均每笔等候审核时长: 直接对所有业务记录求平均
create temporary view pjdhsc as
select COALESCE(round(avg(dhsc)/60,2), 0) as pjsc from dhsc_temp;

--网上开户业务复核时长（总时长）
create temporary view rjfhsc as
select COALESCE(round(sum(w.zfhsc)/60,2), 0) as pjsc from (
select sum(fhsc) as zfhsc,czr from fhsc_temp d group by czr) w;

--网上开户业务平均每笔复核时长: 直接对所有业务记录求平均
create temporary view pjfhsc as
select COALESCE(round(avg(fhsc)/60,2), 0) as pjsc from fhsc_temp;

--网上开户业务平均每笔办理时长: 直接对所有业务记录求平均
create temporary view pjblsc as
select COALESCE(round(avg(sc)/60,2), 0) as pjsc from blsc_temp;

-- ========== 数据插入：总体指标 ==========
-- 插入TIC_YXZB_mapping表：运营指标表，存储总体业务指标
-- 字段说明: idx_id(指标ID), idx_code(指标代码), rq(日期), result(指标值)
upsert into TIC_YXZB_mapping
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,rjshsc b where a.idx_code = 'WSKH_YWSHSC'  -- 网上开户业务审核时长
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,pjshsc b where a.idx_code = 'WSKH_YWPJMBSHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,pjdhsc b where a.idx_code = 'WSKH_YWPJMBDHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,rjfhsc b where a.idx_code = 'WSKH_YWFHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,pjfhsc b where a.idx_code = 'WSKH_YWPJMBFHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,pjblsc b where a.idx_code = 'WSKH_YWPJMBBLSC';

-- ========== 数据插入：人员指标 ==========
-- 插入TIC_YGZB_mapping表：员工指标表，存储按人员统计的指标
-- 字段说明: idx_id(指标ID), idx_code(指标代码), rq(日期), yg(员工), result(指标值)
upsert into TIC_YGZB_mapping
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.czr as yg,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,zshsc b where a.idx_code = 'WSKH_YGYWSHSC'  -- 各审核人员审核时长
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.czr as yg,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,blsc b where a.idx_code = 'WSKH_YGYWPJMBBLSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.czr as yg,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,dhsc b where a.idx_code = 'WSKH_YGYWPJMBDHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.czr as yg,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,shsc b where a.idx_code = 'WSKH_YGYWPJMBSHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.czr as yg,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,zfhsc b where a.idx_code = 'WSKH_YGYWFHSC'
union all
select
  a.id as idx_id,
  a.idx_code as idx_code,
  ${RQ} as rq,
  b.czr as yg,
  b.pjsc as result
from
  TIC_ZBCS_mapping a,fhsc b where a.idx_code = 'WSKH_YGYWPJMBFHSC';
